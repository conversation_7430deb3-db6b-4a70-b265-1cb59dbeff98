import request from '@/utils/request'

/**
 * 预警
 */

// 预警设置

export function warningConfig(data) {
    return request({
        url: '/mesapp-service/warning/config/info',
        method: 'post',
        data: data
    })
}

// 预警设置-默认详情
export function warningDefault(data) {
    return request({
        url: '/mesapp-service/warning/config/default',
        method: 'post',
        data: data
    })
}
// 预警设置-保存
export function warningEdit(data) {
    return request({
        url: '/mesapp-service/warning/config/edit',
        method: 'post',
        data: data
    })
}

// 预警设置-恢复默认
export function warningSetDefault(data) {
    return request({
        url: '/mesapp-service/warning/config/setDefault',
        method: 'post',
        data: data
    })
}

// 预警设置-接收人新增
export function warningReceiverAdd(data) {
    return request({
        url: '/mesapp-service/warning/config/receiver/add',
        method: 'post',
        data: data
    })
}

// 预警设置-接收人删除
export function warningReceiverRemove(data) {
    return request({
        url: '/mesapp-service/warning/config/receiver/remove',
        method: 'post',
        data: data
    })
}

// 预警设置-接收人列表
export function warningReceiverList(data) {
    return request({
        url: '/mesapp-service/warning/config/receiver/list',
        method: 'post',
        data: data
    })
}

// 企业员工列表
export function userList(data) {
    return request({
        url: 'admin-service/system/user/list',
        method: 'get',
        params: data
    })
}

// 获取预警列表
export function getWarningList(data) {
    return request({
        url: '/mesapp-service/warning/list',
        method: 'post',
        data: data
    })
}

// 导出预警报表
export function exportWarningReport(data) {
    return request({
        url: '/mesapp-service/warning/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}

// 获取预警配置
export function warningConfig(data) {
    return request({
        url: '/mesapp-service/warning/config',
        method: 'get',
        params: data
    })
}

// 编辑预警配置
export function warningEdit(data) {
    return request({
        url: '/mesapp-service/warning/config',
        method: 'post',
        data: data
    })
}

// 恢复默认预警配置
export function warningDefault(data) {
    return request({
        url: '/mesapp-service/warning/config/default',
        method: 'post',
        data: data
    })
}

// 获取预警接收人列表
export function warningReceiverList(data) {
    return request({
        url: '/mesapp-service/warning/receiver/list',
        method: 'get',
        params: data
    })
}

// 添加预警接收人
export function warningReceiverAdd(data) {
    return request({
        url: '/mesapp-service/warning/receiver/add',
        method: 'post',
        data: data
    })
}

// 删除预警接收人
export function warningReceiverRemove(data) {
    return request({
        url: '/mesapp-service/warning/receiver/remove',
        method: 'post',
        data: data
    })
}