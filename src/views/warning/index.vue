<template>
  <div class="app-container">
    <!-- 头部操作区 -->
    <WarnHeader
      :currentTab="activeTab"
      @onFilterChange="handleFilterChange"
      @onExport="handleExport"
      @onSettings="handleSettings"
    />

    <!-- 预警标签页 -->
    <WarnTabs
      :activeTab="activeTab"
      :severity="severity"
      @onTabChange="handleTabChange"
    />
  </div>
</template>

<script>
import WarnHeader from './components/WarnHeader.vue'
import WarnTabs from './components/WarnTabs.vue'
// import { exportWarningReport } from '@/api/warning.js'

export default {
  name: 'WarningManagement',
  components: {
    WarnHeader,
    WarnTabs
  },
  data() {
    return {
      activeTab: 'lowStock', // 当前激活的Tab标识
      severity: '' // 当前选择的严重等级
    }
  },
  created() {
    // 初始化时可以设置默认值
  },
  methods: {
    // 处理筛选条件改变
    handleFilterChange(severity) {
      this.severity = severity
    },

    // 处理标签页切换
    handleTabChange(tab) {
      this.activeTab = tab
    },

    // 处理导出报表
    async handleExport() {
      try {
        const tabNames = {
          lowStock: '低库存预警',
          expiry: '保质期预警',
          idleMat: '呆滞料预警'
        }

        console.log('导出' + tabNames[this.activeTab] + '报表')

        // 这里可以调用真实的导出API
        // const response = await exportWarningReport({
        //   type: this.activeTab,
        //   severity: this.severity
        // })

        this.$message.success(`${tabNames[this.activeTab]}报表导出成功`)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请重试')
      }
    },

    // 处理预警设置
    handleSettings() {
      console.log('预警设置')
     
    }
  }
}
</script>

<style scoped lang='scss'>
.app-container {
  padding: 10px;
  background: #F7F8FA;
  min-height: calc(100vh - 84px);
}
</style>
