<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警设置页面演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #F7F8FA;
        }
        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .page-header {
            margin-bottom: 24px;
        }
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1D2129;
            margin: 0;
        }
        .settings-content {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }
        .settings-card {
            background: white;
            border-radius: 8px;
            border: 1px solid #EEEEEE;
            padding: 24px;
        }
        .card-header {
            margin-bottom: 24px;
        }
        .card-title {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 500;
            color: #1D2129;
        }
        .card-title i {
            margin-right: 8px;
            font-size: 18px;
            color: #5672FA;
        }
        .badge {
            background: #FFC700;
            color: #333;
            border-radius: 4px;
            padding: 2px 6px;
            font-size: 12px;
            margin-left: 8px;
            font-weight: normal;
        }
        .form-item {
            margin-bottom: 20px;
        }
        .form-label {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }
        .form-input {
            width: 100px;
            padding: 6px 12px;
            border: 1px solid #D9D9D9;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-tip {
            font-size: 12px;
            color: #86909C;
            margin-top: 4px;
            line-height: 1.4;
        }
        .notification-item {
            margin-bottom: 24px;
        }
        .notification-header {
            margin-bottom: 12px;
        }
        .checkbox {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            color: #1D2129;
        }
        .checkbox input {
            margin-right: 8px;
        }
        .notification-content {
            padding-left: 24px;
        }
        .setting-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .setting-label {
            font-size: 14px;
            color: #86909C;
            margin-right: 16px;
            min-width: 60px;
        }
        .select-box {
            padding: 6px 12px;
            border: 1px solid #D9D9D9;
            border-radius: 4px;
            font-size: 14px;
            width: 200px;
        }
        .receiver-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F0F0F0;
        }
        .receiver-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #F5F5F5;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #86909C;
        }
        .receiver-info {
            flex: 1;
        }
        .receiver-name {
            font-size: 14px;
            font-weight: 500;
            color: #1D2129;
            margin-bottom: 4px;
        }
        .receiver-email {
            font-size: 12px;
            color: #86909C;
        }
        .receiver-actions {
            display: flex;
            gap: 8px;
        }
        .action-btn {
            padding: 4px 8px;
            border: none;
            background: none;
            cursor: pointer;
            color: #86909C;
        }
        .action-btn:hover {
            color: #5672FA;
        }
        .add-receiver {
            margin-top: 20px;
        }
        .btn-primary {
            background: #5672FA;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }
        .add-badge {
            background: #FFC700;
            color: #333;
            border-radius: 4px;
            padding: 2px 6px;
            font-size: 12px;
            margin-left: 8px;
        }
        .footer-actions {
            display: flex;
            justify-content: center;
            gap: 16px;
            padding: 24px 0;
            border-top: 1px solid #EEEEEE;
            background: white;
            border-radius: 8px;
            margin-top: 24px;
        }
        .btn {
            padding: 8px 24px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }
        .btn-default {
            background: #F2F3F5;
            color: #4E5969;
            border: 1px solid #D9D9D9;
        }
        .demo-note {
            background: #E8F4FF;
            border: 1px solid #BEDAFF;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }
        .demo-note-title {
            font-size: 14px;
            font-weight: 500;
            color: #1D2129;
            margin-bottom: 8px;
        }
        .demo-note-content {
            font-size: 14px;
            color: #4E5969;
            line-height: 1.5;
        }
        @media (max-width: 1200px) {
            .settings-content {
                grid-template-columns: 1fr 1fr;
            }
        }
        @media (max-width: 768px) {
            .settings-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-note">
            <div class="demo-note-title">🎉 预警设置页面开发完成</div>
            <div class="demo-note-content">
                已按照原型图完成预警设置页面的开发，包含全局预警阈值设置、通知方式设置和接收人管理三个主要功能模块。
                页面路径：<code>/warning/setting</code>
            </div>
        </div>

        <div class="page-header">
            <h1 class="page-title">预警设置</h1>
        </div>

        <div class="settings-content">
            <!-- 全局预警阈值设置 -->
            <div class="settings-card">
                <div class="card-header">
                    <div class="card-title">
                        <i>⚙️</i>
                        <span>全局预警阈值设置</span>
                        <span class="badge">1</span>
                    </div>
                </div>
                
                <div class="form-item">
                    <label class="form-label">安全库存比例 (%)</label>
                    <input type="number" class="form-input" value="20" />
                    <span style="margin-left: 8px;">%</span>
                    <div class="form-tip">当库存低于此比例时触发预警</div>
                </div>
                
                <div class="form-item">
                    <label class="form-label">保质期预警天数</label>
                    <input type="number" class="form-input" value="30" />
                    <div class="form-tip">在保质期到期前多少天发送预警</div>
                </div>
                
                <div class="form-item">
                    <label class="form-label">呆滞料判定天数</label>
                    <input type="number" class="form-input" value="7" />
                    <div class="form-tip">物料未被动用超过天数判定为呆滞料</div>
                </div>
            </div>

            <!-- 通知方式设置 -->
            <div class="settings-card">
                <div class="card-header">
                    <div class="card-title">
                        <i>🔔</i>
                        <span>通知方式设置</span>
                    </div>
                </div>
                
                <div class="notification-item">
                    <div class="notification-header">
                        <label class="checkbox">
                            <input type="checkbox" checked />
                            电子邮件通知
                        </label>
                    </div>
                    <div class="notification-content">
                        <div class="setting-row">
                            <span class="setting-label">发送频率</span>
                            <select class="select-box">
                                <option>每日汇总</option>
                                <option>实时通知</option>
                                <option>每周汇总</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="notification-item">
                    <div class="notification-header">
                        <label class="checkbox">
                            <input type="checkbox" checked />
                            短信通知
                        </label>
                    </div>
                    <div class="notification-content">
                        <div class="setting-row">
                            <span class="setting-label">优先级</span>
                            <select class="select-box">
                                <option>高 - 所有预警</option>
                                <option>中 - 严重预警</option>
                                <option>低 - 紧急预警</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="notification-item">
                    <div class="notification-header">
                        <label class="checkbox">
                            <input type="checkbox" checked />
                            系统内通知
                        </label>
                    </div>
                </div>
            </div>

            <!-- 接收人管理 -->
            <div class="settings-card">
                <div class="card-header">
                    <div class="card-title">
                        <i>👤</i>
                        <span>接收人管理</span>
                    </div>
                </div>
                
                <div class="receiver-item">
                    <div class="receiver-avatar">张</div>
                    <div class="receiver-info">
                        <div class="receiver-name">张经理</div>
                        <div class="receiver-email"><EMAIL></div>
                    </div>
                    <div class="receiver-actions">
                        <button class="action-btn">✏️</button>
                        <button class="action-btn">🗑️</button>
                    </div>
                </div>

                <div class="receiver-item">
                    <div class="receiver-avatar">李</div>
                    <div class="receiver-info">
                        <div class="receiver-name">李主管</div>
                        <div class="receiver-email"><EMAIL></div>
                    </div>
                    <div class="receiver-actions">
                        <button class="action-btn">✏️</button>
                        <button class="action-btn">🗑️</button>
                    </div>
                </div>

                <div class="add-receiver">
                    <button class="btn-primary">
                        ➕ 添加接收人
                        <span class="add-badge">2</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="footer-actions">
            <button class="btn btn-default">恢复默认</button>
            <button class="btn btn-primary">保存设置</button>
        </div>
    </div>
</body>
</html>
