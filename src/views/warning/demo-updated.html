<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警管理模块 - 按原型设计更新</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 32px;
        }
        .demo-title {
            font-size: 24px;
            font-weight: 600;
            color: #1D2129;
            margin-bottom: 8px;
        }
        .demo-subtitle {
            font-size: 14px;
            color: #86909C;
        }
        .update-section {
            background: #E8F4FF;
            border: 1px solid #BEDAFF;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }
        .update-title {
            font-size: 16px;
            font-weight: 500;
            color: #1D2129;
            margin-bottom: 12px;
        }
        .update-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .update-list li {
            padding: 4px 0;
            color: #4E5969;
            position: relative;
            padding-left: 20px;
        }
        .update-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #52C41A;
            font-weight: bold;
        }
        .mockup-container {
            border: 1px solid #E5E6EB;
            border-radius: 8px;
            padding: 20px;
            background: #F7F8FA;
            margin-bottom: 24px;
        }
        .mockup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #E5E6EB;
        }
        .severity-select {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .severity-label {
            font-size: 14px;
            color: #333;
        }
        .severity-dropdown {
            background: white;
            border: 1px solid #D9D9D9;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 14px;
            position: relative;
        }
        .severity-badge {
            background: #FFC700;
            color: #333;
            border-radius: 4px;
            padding: 2px 6px;
            font-size: 12px;
            margin-left: 8px;
        }
        .header-buttons {
            display: flex;
            gap: 12px;
        }
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            border: none;
            cursor: pointer;
        }
        .btn-primary {
            background: #5672FA;
            color: white;
        }
        .tabs-container {
            margin-bottom: 20px;
        }
        .tabs-nav {
            display: flex;
            border-bottom: 1px solid #E5E6EB;
            margin-bottom: 20px;
        }
        .tab-item {
            padding: 12px 20px 12px 0;
            margin-right: 40px;
            font-size: 16px;
            font-weight: 500;
            color: #86909C;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            position: relative;
        }
        .tab-item.active {
            color: #5672FA;
            border-bottom-color: #5672FA;
        }
        .tab-badge {
            background: #FFC700;
            color: #333;
            border-radius: 4px;
            padding: 2px 6px;
            font-size: 12px;
            margin-left: 8px;
        }
        .warning-card {
            background: white;
            border: 1px solid #EEEEEE;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
        }
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .warning-code {
            font-size: 16px;
            font-weight: 500;
            color: #1D2129;
        }
        .warning-date {
            font-size: 14px;
            color: #86909C;
        }
        .warning-level {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .level-high {
            background: #FF4D4F;
            color: white;
        }
        .level-medium {
            background: #FFC700;
            color: #333;
        }
        .stock-section {
            margin-bottom: 16px;
        }
        .stock-row {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 12px;
        }
        .stock-label {
            font-size: 14px;
            color: #333;
        }
        .stock-value {
            font-size: 14px;
            color: #4E5969;
            font-weight: 500;
        }
        .stock-diff {
            font-size: 14px;
            font-weight: 500;
            margin-left: auto;
        }
        .stock-diff.negative {
            color: #FF4D4F;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #F2F3F5;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 16px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(to right, #5672FA, #165DFF);
            border-radius: 4px;
        }
        .card-actions {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        .btn-default {
            background: #F2F3F5;
            color: #4E5969;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">预警管理模块 - 按原型设计更新</h1>
            <p class="demo-subtitle">根据原型图完成的样式调整和功能优化</p>
        </div>

        <div class="update-section">
            <div class="update-title">🎨 样式更新内容</div>
            <ul class="update-list">
                <li>头部筛选器添加数字标识（严重程度选项显示数量）</li>
                <li>标签页样式调整为下划线风格，移除卡片样式</li>
                <li>呆滞料预警标签页添加数量标识</li>
                <li>预警卡片布局优化，更符合原型设计</li>
                <li>按钮样式统一，使用项目主题色</li>
                <li>进度条样式优化，增加视觉效果</li>
            </ul>
        </div>

        <div class="mockup-container">
            <div class="mockup-header">
                <div class="severity-select">
                    <span class="severity-label">严重程度</span>
                    <div class="severity-dropdown">
                        全部
                        <span class="severity-badge">8</span>
                    </div>
                </div>
                <div class="header-buttons">
                    <button class="btn btn-primary">预警设置</button>
                    <button class="btn btn-primary">📥 导出报表</button>
                </div>
            </div>

            <div class="tabs-container">
                <div class="tabs-nav">
                    <div class="tab-item">低库存预警</div>
                    <div class="tab-item">保质期预警</div>
                    <div class="tab-item active">
                        呆滞料预警
                        <span class="tab-badge">1</span>
                    </div>
                </div>
            </div>

            <div class="warning-card">
                <div class="card-header">
                    <div class="warning-code">物料A-001</div>
                    <div class="warning-date">2025-10-30</div>
                    <div class="warning-level level-high">待处理</div>
                </div>
                
                <div class="stock-section">
                    <div class="stock-row">
                        <span class="stock-label">当前库存：</span>
                        <span class="stock-value">120</span>
                        <span class="stock-label">安全库存：</span>
                        <span class="stock-value">200</span>
                        <span class="stock-diff negative">-80</span>
                    </div>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 60%;"></div>
                    </div>
                </div>
                
                <div class="card-actions">
                    <button class="btn btn-primary btn-small">处理</button>
                    <button class="btn btn-default btn-small">忽略</button>
                </div>
            </div>

            <div class="warning-card">
                <div class="card-header">
                    <div class="warning-code">物料B-205</div>
                    <div class="warning-date">2025-10-30</div>
                    <div class="warning-level level-medium">处理中</div>
                </div>
                
                <div class="stock-section">
                    <div class="stock-row">
                        <span class="stock-label">当前库存：</span>
                        <span class="stock-value">350</span>
                        <span class="stock-label">安全库存：</span>
                        <span class="stock-value">400</span>
                        <span class="stock-diff negative">-50</span>
                    </div>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 87.5%;"></div>
                    </div>
                </div>
                
                <div class="card-actions">
                    <button class="btn btn-primary btn-small">处理</button>
                    <button class="btn btn-default btn-small">忽略</button>
                </div>
            </div>
        </div>

        <div style="background: #F0F9FF; border: 1px solid #BEDAFF; border-radius: 8px; padding: 16px; margin-top: 24px;">
            <div style="font-size: 14px; font-weight: 500; color: #1D2129; margin-bottom: 8px;">✨ 完成状态</div>
            <div style="font-size: 14px; color: #4E5969;">
                预警管理模块已按照原型设计完成样式调整，所有UI元素都符合设计要求。
                可通过路径 <code style="background: #F2F3F5; padding: 2px 6px; border-radius: 4px;">/warning/management</code> 访问。
            </div>
        </div>
    </div>
</body>
</html>
