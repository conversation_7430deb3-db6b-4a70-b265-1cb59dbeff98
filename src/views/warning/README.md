# 预警管理模块

## 功能概述

预警管理模块用于展示和处理三种类型的库存预警：
- 低库存预警
- 保质期预警  
- 呆滞料预警

## 组件结构

```
WarnModule (主组件 - index.vue)
│
├── WarnHeader (头部操作区)
│   ├── 严重程度筛选器
│   ├── 导出报表按钮
│   └── 预警设置按钮
│
└── WarnTabs (预警标签页)
    ├── 低库存预警标签页
    ├── 保质期预警标签页
    ├── 呆滞料预警标签页
    └── WarnList (预警列表)
        └── WarnCard (预警卡片)
```

## 主要功能

### 1. 筛选功能
- 支持按严重程度筛选（严重、中等、轻微）
- 支持按预警类型切换（低库存、保质期、呆滞料）

### 2. 预警卡片展示
- **低库存预警卡片**：显示预警代码、时间、等级、库存数量
- **保质期预警卡片**：显示预警代码、批次号、时间、等级、保质期、状态
- **呆滞料预警卡片**：显示预警代码、时间、等级、当前库存、安全库存、进度条

### 3. 操作功能
- 处理预警
- 忽略预警
- 导出报表
- 预警设置（预留功能）

## 使用方法

### 访问路径
```
/warning/management
```

### API接口

#### 获取预警列表
```javascript
import { getWarningList } from '@/api/warning.js'

const params = {
  pageNum: 1,
  pageSize: 10,
  type: 'lowStock', // lowStock | expiry | idleMat
  severity: 'high'  // high | medium | low | ''
}

const response = await getWarningList(params)
```

#### 导出预警报表
```javascript
import { exportWarningReport } from '@/api/warning.js'

const params = {
  type: 'lowStock',
  severity: 'high'
}

const response = await exportWarningReport(params)
```

## 样式说明

- 使用项目统一的主题色彩
- 卡片采用hover效果
- 严重等级使用不同颜色标识：
  - 严重：红色 (#F80000)
  - 中等：橙色 (#F85300)  
  - 轻微：蓝色 (#5672FA)

## 开发说明

### 模拟数据
当前使用模拟数据进行展示，实际使用时需要：
1. 取消注释API调用代码
2. 注释掉模拟数据相关代码
3. 根据实际API返回格式调整数据处理逻辑

### 扩展功能
- 预警设置功能预留了点击事件，可根据需求实现
- 支持添加更多预警类型
- 支持自定义预警卡片布局
