预警管理 (Warn) 模块开发文档
1. 概述
该模块用于展示和处理三种类型的库存预警：低库存、保质期和呆滞料。核心功能包括按严重程度筛选、按类型切换预警列表、以及导出报表。
2. 组件设计
WarnModule (主组件)
│
├── WarnHeader (头部操作区)
│
└── WarnTabs (预警标签页)
    └── WarnList (预警列表)
        └── WarnCard (预警卡片)



3. 组件详述
3.1. WarnModule (主组件)
职责: 模块根组件，管理核心状态。
State:
activeTab: string - 当前激活的Tab标识 (e.g., 'lowStock')。
severity: string - 当前选择的严重等级。
3.2. WarnHeader (头部操作区)
职责: 渲染筛选器与功能按钮。
Props:
currentTab: string - 当前激活的Tab标识，用于导出时区分。
Events:
onFilterChange(severity): 筛选条件改变时触发。
onExport(): 点击“导出报表”时触发。
逻辑: 根据 currentTab 打印信息，例如: console.log('导出' + currentTab + '报表')。
onSettings(): 点击“预警设置”时触发。
逻辑: 预留点击事件，暂不实现功能。
3.3. WarnTabs (预警标签页)
职责: 渲染Tab，并根据 activeTab 状态显示对应的 WarnList 组件。
Props:
severity: string - 将筛选条件透传给 WarnList。
逻辑: 切换Tab时，更新 WarnModule 中的 activeTab 状态。
3.4. WarnList (预警列表)
职责: 获取并渲染指定类型的预警列表。
Props:
type: string - 预警类型 (e.g., 'lowStock', 'expiry', 'idleMat')。
severity: string - 严重等级筛选条件。
API调用:
组件挂载时，或当 type / severity props 变化时，根据 type 调用对应接口获取数据。
例如: fetchApi({ type: props.type, severity: props.severity })。
3.5. WarnCard (预警卡片)
职责: 展示单条预警的详细信息。
Props:
data: object - 单条预警的数据。
type: string - 预警类型，用于决定卡片的内部布局。
