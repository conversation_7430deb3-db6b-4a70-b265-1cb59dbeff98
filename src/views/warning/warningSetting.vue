<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">预警设置</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="settings-content">
      <!-- 全局预警阈值设置 -->
      <el-card shadow="never" class="settings-card">
        <div class="card-header">
          <div class="card-title">
            <i class="el-icon-setting"></i>
            <span>全局预警阈值设置</span>
            <span class="badge">1</span>
          </div>
        </div>

        <div class="settings-form">
          <el-form :model="thresholdForm" label-width="140px" size="small">
            <el-form-item label="安全库存比例 (%)">
              <div class="input-with-suffix">
                <el-input-number
                  v-model="thresholdForm.safeStockRatio"
                  :min="0"
                  :max="100"
                  :precision="0"
                  controls-position="right"
                  class="threshold-input"
                />
                <span class="input-suffix">%</span>
              </div>
              <div class="form-tip">当库存低于此比例时触发预警</div>
            </el-form-item>

            <el-form-item label="保质期预警天数">
              <div class="input-with-icon">
                <el-input-number
                  v-model="thresholdForm.expiryDays"
                  :min="1"
                  :max="365"
                  :precision="0"
                  controls-position="right"
                  class="threshold-input"
                />
                <i class="el-icon-date input-icon"></i>
              </div>
              <div class="form-tip">在保质期到期前多少天发送预警</div>
            </el-form-item>

            <el-form-item label="呆滞料判定天数">
              <div class="input-with-icon">
                <el-input-number
                  v-model="thresholdForm.idleDays"
                  :min="1"
                  :max="999"
                  :precision="0"
                  controls-position="right"
                  class="threshold-input"
                />
                <i class="el-icon-info input-icon"></i>
              </div>
              <div class="form-tip">物料未被动用超过天数判定为呆滞料</div>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <!-- 通知方式设置 -->
      <el-card shadow="never" class="settings-card">
        <div class="card-header">
          <div class="card-title">
            <i class="el-icon-bell"></i>
            <span>通知方式设置</span>
          </div>
        </div>

        <div class="notification-settings">
          <!-- 电子邮件通知 -->
          <div class="notification-item">
            <div class="notification-header">
              <el-checkbox v-model="notificationForm.emailEnabled" size="medium">
                电子邮件通知
              </el-checkbox>
            </div>
            <div class="notification-content" v-if="notificationForm.emailEnabled">
              <div class="setting-row">
                <span class="setting-label">发送频率</span>
                <el-select v-model="notificationForm.emailFrequency" size="small" style="width: 200px;">
                  <el-option label="每日汇总" value="daily"></el-option>
                  <el-option label="实时通知" value="realtime"></el-option>
                  <el-option label="每周汇总" value="weekly"></el-option>
                </el-select>
              </div>
            </div>
          </div>

          <!-- 短信通知 -->
          <div class="notification-item">
            <div class="notification-header">
              <el-checkbox v-model="notificationForm.smsEnabled" size="medium">
                短信通知
              </el-checkbox>
            </div>
            <div class="notification-content" v-if="notificationForm.smsEnabled">
              <div class="setting-row">
                <span class="setting-label">优先级</span>
                <el-select v-model="notificationForm.smsPriority" size="small" style="width: 200px;">
                  <el-option label="高 - 所有预警" value="high"></el-option>
                  <el-option label="中 - 严重预警" value="medium"></el-option>
                  <el-option label="低 - 紧急预警" value="low"></el-option>
                </el-select>
              </div>
            </div>
          </div>

          <!-- 系统内通知 -->
          <div class="notification-item">
            <div class="notification-header">
              <el-checkbox v-model="notificationForm.systemEnabled" size="medium">
                系统内通知
              </el-checkbox>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 接收人管理 -->
      <el-card shadow="never" class="settings-card">
        <div class="card-header">
          <div class="card-title">
            <i class="el-icon-user"></i>
            <span>接收人管理</span>
          </div>
        </div>

        <div class="receivers-management">
          <!-- 接收人列表 -->
          <div class="receivers-list">
            <div
              v-for="(receiver, index) in receivers"
              :key="receiver.id"
              class="receiver-item"
            >
              <div class="receiver-avatar">
                <img :src="receiver.avatar || defaultAvatar" :alt="receiver.name" />
              </div>
              <div class="receiver-info">
                <div class="receiver-name">{{ receiver.name }}</div>
                <div class="receiver-email">{{ receiver.email }}</div>
              </div>
              <div class="receiver-actions">
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  size="small"
                  @click="editReceiver(receiver)"
                >
                </el-button>
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  size="small"
                  @click="removeReceiver(index)"
                >
                </el-button>
              </div>
            </div>
          </div>

          <!-- 添加接收人按钮 -->
          <div class="add-receiver">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="small"
              @click="showAddReceiverDialog"
            >
              添加接收人
              <span class="add-badge">{{ receivers.length }}</span>
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 底部操作按钮 -->
    <div class="footer-actions">
      <el-button size="medium" @click="resetSettings">恢复默认</el-button>
      <el-button type="primary" size="medium" @click="saveSettings">保存设置</el-button>
    </div>

    <!-- 添加接收人对话框 -->
    <el-dialog
      title="添加接收人"
      :visible.sync="addReceiverDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="newReceiver" label-width="80px" size="small">
        <el-form-item label="姓名" required>
          <el-input v-model="newReceiver.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" required>
          <el-input v-model="newReceiver.email" placeholder="请输入邮箱地址"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addReceiverDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addReceiver">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { warningConfig, warningEdit, warningSetDefault, warningReceiverList, warningReceiverAdd, warningReceiverRemove } from '@/api/warning.js'

export default {
  name: 'WarningSetting',
  data() {
    return {
      // 阈值设置表单
      thresholdForm: {
        safeStockRatio: 20,
        expiryDays: 30,
        idleDays: 7
      },

      // 通知方式设置表单
      notificationForm: {
        emailEnabled: true,
        emailFrequency: 'daily',
        smsEnabled: true,
        smsPriority: 'high',
        systemEnabled: true
      },

      // 接收人列表
      receivers: [
        {
          id: 1,
          name: '张经理',
          email: '<EMAIL>',
          avatar: ''
        },
        {
          id: 2,
          name: '李主管',
          email: '<EMAIL>',
          avatar: ''
        }
      ],

      // 添加接收人对话框
      addReceiverDialogVisible: false,
      newReceiver: {
        name: '',
        email: ''
      },

      // 默认头像
      defaultAvatar: require('@/assets/images/profile.jpg'),

      loading: false
    }
  },

  created() {
    this.loadSettings()
    this.loadReceivers()
  },

  methods: {
    // 加载设置
    async loadSettings() {
      try {
        // 预留API接口调用，暂不请求
        // const response = await warningConfig({})
        // if (response.code === 200) {
        //   this.thresholdForm = response.data.threshold
        //   this.notificationForm = response.data.notification
        // }
        console.log('预留：加载预警设置')
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    },

    // 加载接收人列表
    async loadReceivers() {
      try {
        // 预留API接口调用，暂不请求
        // const response = await warningReceiverList({})
        // if (response.code === 200) {
        //   this.receivers = response.data
        // }
        console.log('预留：加载接收人列表')
      } catch (error) {
        console.error('加载接收人列表失败:', error)
      }
    },

    // 保存设置
    async saveSettings() {
      this.loading = true
      try {
        // 预留API接口调用，暂不请求
        // const settingsData = {
        //   threshold: this.thresholdForm,
        //   notification: this.notificationForm
        // }
        // const response = await warningEdit(settingsData)
        // if (response.code === 200) {
        //   this.$message.success('设置保存成功')
        // }

        console.log('预留：保存预警设置', {
          threshold: this.thresholdForm,
          notification: this.notificationForm
        })
        this.$message.success('设置保存成功')
      } catch (error) {
        console.error('保存设置失败:', error)
        this.$message.error('保存设置失败')
      } finally {
        this.loading = false
      }
    },

    // 恢复默认设置
    async resetSettings() {
      this.$confirm('确定要恢复默认设置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 预留API接口调用，暂不请求
          // const response = await warningSetDefault({})
          // if (response.code === 200) {
          //   this.loadSettings()
          //   this.$message.success('已恢复默认设置')
          // }

          console.log('预留：恢复默认设置')
          // 模拟恢复默认
          this.thresholdForm = {
            safeStockRatio: 20,
            expiryDays: 30,
            idleDays: 7
          }
          this.notificationForm = {
            emailEnabled: true,
            emailFrequency: 'daily',
            smsEnabled: true,
            smsPriority: 'high',
            systemEnabled: true
          }
          this.$message.success('已恢复默认设置')
        } catch (error) {
          console.error('恢复默认设置失败:', error)
          this.$message.error('恢复默认设置失败')
        }
      })
    },

    // 显示添加接收人对话框
    showAddReceiverDialog() {
      this.newReceiver = {
        name: '',
        email: ''
      }
      this.addReceiverDialogVisible = true
    },

    // 添加接收人
    async addReceiver() {
      if (!this.newReceiver.name || !this.newReceiver.email) {
        this.$message.warning('请填写完整信息')
        return
      }

      try {
        // 预留API接口调用，暂不请求
        // const response = await warningReceiverAdd(this.newReceiver)
        // if (response.code === 200) {
        //   this.loadReceivers()
        //   this.addReceiverDialogVisible = false
        //   this.$message.success('添加接收人成功')
        // }

        console.log('预留：添加接收人', this.newReceiver)
        // 模拟添加成功
        const newId = Math.max(...this.receivers.map(r => r.id)) + 1
        this.receivers.push({
          id: newId,
          name: this.newReceiver.name,
          email: this.newReceiver.email,
          avatar: ''
        })
        this.addReceiverDialogVisible = false
        this.$message.success('添加接收人成功')
      } catch (error) {
        console.error('添加接收人失败:', error)
        this.$message.error('添加接收人失败')
      }
    },

    // 编辑接收人
    editReceiver() {
      this.$message.info('编辑功能开发中...')
    },

    // 删除接收人
    async removeReceiver(index) {
      this.$confirm('确定要删除此接收人吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 预留API接口调用，暂不请求
          // const receiver = this.receivers[index]
          // const response = await warningReceiverRemove({ id: receiver.id })
          // if (response.code === 200) {
          //   this.receivers.splice(index, 1)
          //   this.$message.success('删除成功')
          // }

          console.log('预留：删除接收人', this.receivers[index])
          // 模拟删除成功
          this.receivers.splice(index, 1)
          this.$message.success('删除成功')
        } catch (error) {
          console.error('删除接收人失败:', error)
          this.$message.error('删除失败')
        }
      })
    }
  }
}
</script>

<style scoped lang='scss'>
@import '@/assets/css/theme.scss';

.app-container {
  padding: 20px;
  background: #F7F8FA;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 24px;

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: $title_text_color;
    margin: 0;
  }
}

.settings-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.settings-card {
  border-radius: 8px;
  border: 1px solid #EEEEEE;

  ::v-deep .el-card__body {
    padding: 24px;
  }

  .card-header {
    margin-bottom: 24px;

    .card-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: $title_text_color;

      i {
        margin-right: 8px;
        font-size: 18px;
        color: $theme_color;
      }

      .badge {
        background: #FFC700;
        color: #333;
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 12px;
        margin-left: 8px;
        font-weight: normal;
      }
    }
  }
}

.settings-form {
  .input-with-suffix {
    position: relative;
    display: flex;
    align-items: center;

    .threshold-input {
      flex: 1;
    }

    .input-suffix {
      margin-left: 8px;
      color: $value_color;
      font-size: 14px;
    }
  }

  .input-with-icon {
    position: relative;

    .input-icon {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      color: $value_color;
      pointer-events: none;
    }
  }

  .form-tip {
    font-size: 12px;
    color: $label_color;
    margin-top: 4px;
    line-height: 1.4;
  }
}

.notification-settings {
  .notification-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .notification-header {
      margin-bottom: 12px;

      ::v-deep .el-checkbox__label {
        font-size: 14px;
        font-weight: 500;
        color: $title_text_color;
      }
    }

    .notification-content {
      padding-left: 24px;

      .setting-row {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .setting-label {
          font-size: 14px;
          color: $label_color;
          margin-right: 16px;
          min-width: 60px;
        }
      }
    }
  }
}

.receivers-management {
  .receivers-list {
    margin-bottom: 20px;

    .receiver-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #F0F0F0;

      &:last-child {
        border-bottom: none;
      }

      .receiver-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 12px;
        background: #F5F5F5;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .receiver-info {
        flex: 1;

        .receiver-name {
          font-size: 14px;
          font-weight: 500;
          color: $title_text_color;
          margin-bottom: 4px;
        }

        .receiver-email {
          font-size: 12px;
          color: $value_color;
        }
      }

      .receiver-actions {
        display: flex;
        gap: 8px;

        .el-button {
          padding: 4px;

          &:hover {
            color: $theme_color;
          }
        }
      }
    }
  }

  .add-receiver {
    .add-badge {
      background: #FFC700;
      color: #333;
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 12px;
      margin-left: 8px;
    }
  }
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 0;
  border-top: 1px solid #EEEEEE;
  background: white;
  border-radius: 8px;
  margin-top: 24px;
}

// 响应式设计
@media (max-width: 1200px) {
  .settings-content {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .settings-content {
    grid-template-columns: 1fr;
  }

  .app-container {
    padding: 16px;
  }
}
</style>