<template>
  <el-card shadow="never" class="form_box">
    <el-row :gutter="20">
      <el-col :span="18">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="严重程度" prop="severity">
            <el-select 
              v-model="queryParams.severity" 
              placeholder="请选择严重程度" 
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="严重" value="high"></el-option>
              <el-option label="中等" value="medium"></el-option>
              <el-option label="轻微" value="low"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="6" class="fend">
        <el-button 
          class="default_btn" 
          icon="el-icon-download" 
          size="mini" 
          @click="handleExport"
        >
          导出报表
        </el-button>
        <el-button 
          class="default_device_btn" 
          circle 
          icon="el-icon-setting" 
          size="mini" 
          @click="handleSettings"
        >
        </el-button>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
export default {
  name: 'WarnHeader',
  props: {
    currentTab: {
      type: String,
      default: 'lowStock'
    }
  },
  data() {
    return {
      queryParams: {
        severity: ''
      }
    }
  },
  methods: {
    // 处理筛选条件改变
    handleFilterChange() {
      this.$emit('onFilterChange', this.queryParams.severity)
    },
    
    // 处理查询
    handleQuery() {
      this.handleFilterChange()
    },
    
    // 重置查询
    resetQuery() {
      this.queryParams.severity = ''
      this.handleFilterChange()
    },
    
    // 处理导出
    handleExport() {
      this.$emit('onExport')
    },
    
    // 处理设置
    handleSettings() {
      this.$emit('onSettings')
    }
  }
}
</script>

<style scoped lang='scss'>
.fend {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}
</style>
