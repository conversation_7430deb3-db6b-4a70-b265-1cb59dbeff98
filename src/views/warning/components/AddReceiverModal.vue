<template>
  <div class="dialog_box">
    <el-dialog
      title="添加接收人"
      :visible.sync="dialogVisible"
      @close="resetForm"
      width="60%"
    >
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="queryParams" size="small" :inline="true">
          <el-form-item>
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入用户名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 用户表格 -->
      <el-table
        ref="userTable"
        :data="userList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        height="400"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="userName" label="用户名" min-width="120" />
        <el-table-column prop="nickName" label="姓名" min-width="120" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="phonenumber" label="手机号" min-width="120" />
        <el-table-column prop="deptName" label="部门" min-width="120" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'" size="mini">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        style="margin-top: 16px;"
      />

      <!-- 底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetForm">取消</el-button>
        <el-button type="primary" @click="submitForm" :disabled="selectedUsers.length === 0">
          确定添加 ({{ selectedUsers.length }})
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { userList } from '@/api/warning.js'

export default {
  name: 'AddReceiverModal',
  data() {
    return {
      dialogVisible: false,
      loading: false,
      userList: [],
      selectedUsers: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: ''
      }
    }
  },
  methods: {
    // 显示对话框
    showModal() {
      this.dialogVisible = true
      this.getList()
    },

    // 获取用户列表
    async getList() {
      this.loading = true
      try {
        // 预留API接口调用，暂不请求
        // const response = await userList(this.queryParams)
        // if (response.code === 200) {
        //   this.userList = response.rows
        //   this.total = response.total
        // }
        
        console.log('预留：获取用户列表', this.queryParams)
        
        // 模拟数据
        this.userList = [
          {
            userId: 1,
            userName: 'admin',
            nickName: '管理员',
            email: '<EMAIL>',
            phonenumber: '13800138000',
            deptName: '总经办',
            status: '0'
          },
          {
            userId: 2,
            userName: 'zhangsan',
            nickName: '张三',
            email: '<EMAIL>',
            phonenumber: '13800138001',
            deptName: '生产部',
            status: '0'
          },
          {
            userId: 3,
            userName: 'lisi',
            nickName: '李四',
            email: '<EMAIL>',
            phonenumber: '13800138002',
            deptName: '质检部',
            status: '0'
          },
          {
            userId: 4,
            userName: 'wangwu',
            nickName: '王五',
            email: '<EMAIL>',
            phonenumber: '13800138003',
            deptName: '仓储部',
            status: '0'
          },
          {
            userId: 5,
            userName: 'zhaoliu',
            nickName: '赵六',
            email: '<EMAIL>',
            phonenumber: '13800138004',
            deptName: '采购部',
            status: '1'
          }
        ]
        this.total = 5
      } catch (error) {
        console.error('获取用户列表失败:', error)
        this.$message.error('获取用户列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置搜索
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        userName: ''
      }
      this.getList()
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedUsers = selection
    },

    // 提交表单
    async submitForm() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请选择要添加的接收人')
        return
      }

      try {
        const receiveUserIdLists = this.selectedUsers.map(user => user.userId)
        
        console.log('预留：添加接收人', { receiveUserIdLists })
        
        // 预留API接口调用，暂不请求
        // const response = await warningReceiverAdd({ receiveUserIdLists })
        // if (response.code === 200) {
        //   this.$message.success('添加接收人成功')
        //   this.$emit('refresh')
        //   this.resetForm()
        // }
        
        // 模拟成功
        this.$message.success(`成功添加 ${this.selectedUsers.length} 个接收人`)
        this.$emit('refresh')
        this.resetForm()
      } catch (error) {
        console.error('添加接收人失败:', error)
        this.$message.error('添加接收人失败')
      }
    },

    // 重置表单
    resetForm() {
      this.dialogVisible = false
      this.selectedUsers = []
      this.userList = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        userName: ''
      }
      // 清空表格选择
      this.$nextTick(() => {
        if (this.$refs.userTable) {
          this.$refs.userTable.clearSelection()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.search-area {
  margin-bottom: 16px;
  padding: 16px;
  background: #F7F8FA;
  border-radius: 4px;
}

.dialog-footer {
  text-align: center;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-table {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}
</style>
