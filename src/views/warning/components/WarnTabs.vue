<template>
  <el-card shadow="never" class="table_box">
    <el-row class="mb8 form_btn">
      <el-col class="tabs-box">
        <el-tabs 
          v-model="currentTab" 
          type="card" 
          @tab-click="handleTabClick"
          class="mb20"
        >
          <el-tab-pane label="低库存预警" name="lowStock"></el-tab-pane>
          <el-tab-pane label="保质期预警" name="expiry"></el-tab-pane>
          <el-tab-pane label="呆滞料预警" name="idleMat"></el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    
    <!-- 预警列表 -->
    <WarnList 
      :type="currentTab"
      :severity="severity"
    />
  </el-card>
</template>

<script>
import WarnList from './WarnList.vue'

export default {
  name: 'WarnTabs',
  components: {
    WarnList
  },
  props: {
    activeTab: {
      type: String,
      default: 'lowStock'
    },
    severity: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentTab: this.activeTab
    }
  },
  watch: {
    activeTab(newVal) {
      this.currentTab = newVal
    }
  },
  methods: {
    // 处理标签页点击
    handleTabClick(tab) {
      this.currentTab = tab.name
      this.$emit('onTabChange', tab.name)
    }
  }
}
</script>

<style scoped lang='scss'>
.form_btn {
  margin-bottom: 20px;
}
</style>
