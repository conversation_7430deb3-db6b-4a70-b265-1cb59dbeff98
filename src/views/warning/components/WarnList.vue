<template>
  <div class="warn-list">
    <div v-loading="loading" class="list-container">
      <div v-if="warningList.length > 0">
        <WarnCard
          v-for="item in warningList"
          :key="item.id"
          :data="item"
          :type="type"
        />
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <el-empty description="暂无预警数据"></el-empty>
      </div>
    </div>
    
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import WarnCard from './WarnCard.vue'
import { getWarningList } from '@/api/warning.js'

export default {
  name: 'WarnList',
  components: {
    WarnCard
  },
  props: {
    type: {
      type: String,
      required: true
    },
    severity: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      warningList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  watch: {
    type: {
      handler() {
        this.getList()
      },
      immediate: true
    },
    severity() {
      this.queryParams.pageNum = 1
      this.getList()
    }
  },
  methods: {
    // 获取预警列表
    async getList() {
      this.loading = true
      try {
        const params = {
          ...this.queryParams,
          type: this.type,
          severity: this.severity
        }
        
        // 模拟API调用，实际应该调用真实接口
        // const response = await getWarningList(params)
        
        // 模拟数据
        const mockData = this.getMockData()
        this.warningList = mockData.list
        this.total = mockData.total
        
      } catch (error) {
        console.error('获取预警列表失败:', error)
        this.$message.error('获取预警列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取模拟数据
    getMockData() {
      const mockDataMap = {
        lowStock: {
          list: [
            {
              id: 1,
              code: 'TRHF-517',
              createTime: '2022-12-15',
              level: 'high',
              levelText: '严重',
              count: 'V2,450',
              actions: ['处理', '忽略']
            },
            {
              id: 2,
              code: 'TRHF-623',
              createTime: '2023-02-28',
              level: 'medium',
              levelText: '中等',
              count: 'V850',
              actions: ['处理', '忽略']
            }
          ],
          total: 2
        },
        expiry: {
          list: [
            {
              id: 3,
              code: 'TRHF-268',
              batch: 'EXP20220505',
              createTime: '2022-05-20',
              expiryDate: '2022-05-30',
              level: 'high',
              levelText: '严重',
              daysLeft: -5,
              status: '已过期',
              actions: ['处理', '忽略']
            },
            {
              id: 4,
              code: 'TRHF-472',
              batch: 'EXP20220506',
              createTime: '2022-05-05',
              expiryDate: '2022-05-15',
              level: 'medium',
              levelText: '中等',
              daysLeft: 3,
              status: '即将过期',
              actions: ['处理', '忽略']
            }
          ],
          total: 2
        },
        idleMat: {
          list: [
            {
              id: 5,
              code: '物料A-001',
              createTime: '2025-10-30',
              level: 'high',
              levelText: '待处理',
              currentStock: 120,
              safeStock: 200,
              idleDays: 90,
              actions: ['处理', '忽略']
            },
            {
              id: 6,
              code: '物料B-205',
              createTime: '2025-10-30',
              level: 'medium',
              levelText: '处理中',
              currentStock: 350,
              safeStock: 400,
              idleDays: 60,
              actions: ['处理', '忽略']
            }
          ],
          total: 2
        }
      }
      
      return mockDataMap[this.type] || { list: [], total: 0 }
    }
  }
}
</script>

<style scoped lang='scss'>
.warn-list {
  .list-container {
    min-height: 400px;
  }

  .warning-cards {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }
}
</style>
